<script setup lang="ts">
// 方案互动
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, computed, nextTick, defineProps } from 'vue';
import { DemandSubmitObj, ProcessNode, MiceTypeConstant } from '@haierbusiness-front/common-libs';

const props = defineProps({
  demandInfo: {
    type: Object,
    default: {},
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

// 🔥 监听 demandInfo 变化
watch(() => props.demandInfo, (newVal) => {
 
}, { immediate: true, deep: true });

// 复制文本到剪贴板
const getCopy = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success('复制成功！');
  } catch (err) {
    message.success('复制失败');
  }
};

onMounted(async () => {
  // console.log('🔥 billUploadschemeInfo 组件已挂载，当前 demandInfo:', props.demandInfo);
});
</script>

<template>
  <!-- 方案互动 - 头部 -->
  <div class="scheme_info">
    <div class="interact_mice_title">
      <div class="interact_mice_name_img mr8"></div>
      <div class="interact_mice_name mr24">
        {{ props.demandInfo?.miceName || '暂无会议名称' }}
      </div>
      <!-- <div class="interact_mice_type">
        {{ ProcessNode.ofType(props.demandInfo?.processNode)?.desc || '' }}
      </div> -->
    </div>
    <div class="interact_mice_num mt12">
      <span class="mr10">会议编号：{{ props.demandInfo?.mainCode || '暂无编号' }}</span>
      <img @click="getCopy(props.demandInfo?.mainCode || '')" src="@/assets/image/scheme/copy_blue.png" width="16" />
    </div>

    <!-- 🔥 调试信息显示 -->
    <!-- <div class="debug-info" style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 12px;">
      <div><strong>调试信息:</strong></div>
      <div>miceName: {{ props.demandInfo?.miceName || '无' }}</div>
      <div>mainCode: {{ props.demandInfo?.mainCode || '无' }}</div>
      <div>personTotal: {{ props.demandInfo?.personTotal || '无' }}</div>
      <div>miceType: {{ props.demandInfo?.miceType || '无' }}</div>
      <div>startDate: {{ props.demandInfo?.startDate || '无' }}</div>
      <div>endDate: {{ props.demandInfo?.endDate || '无' }}</div>
      <div>数据对象: {{ JSON.stringify(props.demandInfo) }}</div>
    </div> -->

    <a-row class="interact_mice_info mt24">
      <a-col :span="12">
        <span class="mice_info_title mice_info_person_img">会议人数：</span>
        <span class="mice_info_value">
          {{ props.demandInfo?.personTotal ? props.demandInfo.personTotal + '人' : '-' }}
        </span>
      </a-col>
      <a-col :span="12">
        <span class="mice_info_title mice_info_type_img">会议类型：</span>
        <span class="mice_info_value">
          {{ MiceTypeConstant.ofType(props.demandInfo?.miceType)?.desc || '-' }}
        </span>
      </a-col>

      <a-col :span="12" class="mt12">
        <span class="mice_info_title mice_info_time_img">需求开始时间：</span>
        <span class="mice_info_value">
          {{ props.demandInfo?.startDate || '-' }}
        </span>
      </a-col>
      <a-col :span="12" class="mt12">
        <span class="mice_info_title mice_info_time_img">需求结束时间：</span>
        <span class="mice_info_value">
          {{ props.demandInfo?.endDate || '-' }}
        </span>
      </a-col>
    </a-row>
  </div>
</template>

<style scoped lang="less">
.scheme_info {
  padding: 24px 32px;

  width: 100%;
  height: 100%;
  background: url('@/assets/image/scheme/mice_bgc.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  border-radius: 6px;

  .interact_mice_title {
    display: flex;
    align-items: center;

    .interact_mice_name_img {
      width: 28px;
      height: 28px;
      background-image: url('@/assets/image/scheme/mice_name.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .interact_mice_name {
      font-family: PingFangSCSemibold, PingFangSCSemibold;
      font-weight: normal;
      font-size: 20px;
      color: #1d2129;
      line-height: 28px;
    }

    .interact_mice_type {
      width: 108px;
      height: 28px;
      line-height: 28px;
      text-align: center;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #ffffff;
      background: #1868db;
      border-radius: 4px;
    }
  }

  .interact_mice_num {
    display: flex;
    align-items: center;

    font-size: 14px;
    color: #86909c;
    line-height: 20px;

    img {
      cursor: pointer;
    }
  }
  .interact_mice_info {
    width: 50%;
    font-size: 14px;
    color: #86909c;
    line-height: 20px;

    .mice_info_title {
      display: inline-block;
      text-indent: 26px;
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center left;
    }
    .mice_info_person_img {
      background-image: url('@/assets/image/scheme/mice_person.png');
    }

    .mice_info_type_img {
      background-image: url('@/assets/image/scheme/mice_type.png');
    }

    .mice_info_time_img {
      background-image: url('@/assets/image/scheme/mice_time.png');
    }

    .mice_info_value {
      color: #1d2129;
    }
  }
}
</style>
